# 新闻列表 API 使用示例

## 基本用法

### 1. 获取第一页新闻列表（默认参数）

```bash
curl -X POST http://localhost:3000/api/news \
  -H "Content-Type: application/json" \
  -d '{}'
```

### 2. 获取指定页码和每页数量的新闻列表

```bash
curl -X POST http://localhost:3000/api/news \
  -H "Content-Type: application/json" \
  -d '{
    "page": 2,
    "pageSize": 15
  }'
```

### 3. 使用 limit 参数（与 pageSize 功能相同）

```bash
curl -X POST http://localhost:3000/api/news \
  -H "Content-Type: application/json" \
  -d '{
    "page": 1,
    "limit": 20
  }'
```

### 4. 按分类筛选新闻

```bash
curl -X POST http://localhost:3000/api/news \
  -H "Content-Type: application/json" \
  -d '{
    "categoryId": 1,
    "pageSize": 10
  }'
```

### 5. 组合查询（分页 + 分类筛选）

```bash
curl -X POST http://localhost:3000/api/news \
  -H "Content-Type: application/json" \
  -d '{
    "page": 3,
    "pageSize": 8,
    "categoryId": 1
  }'
```

## JavaScript/TypeScript 客户端示例

### 使用 fetch API

```typescript
// 基本查询
async function getNewsList(params = {}) {
  try {
    const response = await fetch('/api/news', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params)
    });
    
    const result = await response.json();
    
    if (result.success) {
      console.log('查询成功:', result.data);
      return result.data;
    } else {
      console.error('查询失败:', result.error);
      return null;
    }
  } catch (error) {
    console.error('请求失败:', error);
    return null;
  }
}

// 使用示例
const newsData = await getNewsList({
  page: 1,
  pageSize: 10,
  categoryId: 1
});
```

### 使用 axios

```typescript
import axios from 'axios';

async function getNewsListWithAxios(params = {}) {
  try {
    const response = await axios.post('/api/news', params);
    
    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.error);
    }
  } catch (error) {
    console.error('查询新闻列表失败:', error);
    throw error;
  }
}
```

## Vue.js 组件示例

```vue
<template>
  <div class="news-list">
    <div v-if="loading">加载中...</div>
    <div v-else-if="error" class="error">{{ error }}</div>
    <div v-else>
      <div v-for="news in newsList" :key="news.id" class="news-item">
        <h3>{{ news.title }}</h3>
        <p>{{ news.introduction }}</p>
        <small>发布时间: {{ news.publishTime }}</small>
      </div>
      
      <!-- 分页组件 -->
      <div class="pagination">
        <button 
          @click="loadPage(currentPage - 1)" 
          :disabled="currentPage <= 1"
        >
          上一页
        </button>
        <span>第 {{ currentPage }} 页，共 {{ totalPages }} 页</span>
        <button 
          @click="loadPage(currentPage + 1)" 
          :disabled="currentPage >= totalPages"
        >
          下一页
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';

const newsList = ref([]);
const loading = ref(false);
const error = ref('');
const currentPage = ref(1);
const totalPages = ref(0);
const pageSize = ref(10);

async function loadNewsList(page = 1, categoryId = null) {
  loading.value = true;
  error.value = '';
  
  try {
    const params = {
      page,
      pageSize: pageSize.value
    };
    
    if (categoryId) {
      params.categoryId = categoryId;
    }
    
    const response = await fetch('/api/news', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params)
    });
    
    const result = await response.json();
    
    if (result.success) {
      newsList.value = result.data.data;
      currentPage.value = result.data.page;
      totalPages.value = result.data.totalPages;
    } else {
      error.value = result.error || '查询失败';
    }
  } catch (err) {
    error.value = '网络错误，请稍后重试';
    console.error('查询新闻列表失败:', err);
  } finally {
    loading.value = false;
  }
}

function loadPage(page) {
  if (page >= 1 && page <= totalPages.value) {
    loadNewsList(page);
  }
}

onMounted(() => {
  loadNewsList();
});
</script>
```

## 响应数据结构

```typescript
interface NewsListResponse {
  success: boolean;
  data: {
    data: NewsListItem[];     // 新闻列表数组
    total: number;            // 总记录数
    page: number;             // 当前页码
    pageSize: number;         // 每页数量
    totalPages: number;       // 总页数
  };
  msg: string;
  timestamp: string;
}

interface NewsListItem {
  id: string;
  title: string | null;
  shortTitle: string | null;
  introduction: string | null;
  picture: string | null;
  source: string | null;
  publishTime: string | null;
  viewCount: number | null;
  isPublish: number | null;
  cateId: number | null;
  newsClassifys: string | null;
  newsLabels: string | null;
  createdAt: string | null;
  updatedAt: string | null;
}
```

## 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| page | number | 1 | 页码，最小为1 |
| pageSize | number | 10 | 每页数量，范围1-100 |
| limit | number | 10 | 每页数量的别名，与pageSize功能相同 |
| categoryId | number | - | 分类ID，用于按分类筛选资讯 |

## 错误处理

API 会返回以下类型的错误：

- **400 Bad Request**: 请求参数格式错误
- **500 Internal Server Error**: 服务器内部错误

错误响应格式：
```json
{
  "success": false,
  "error": "错误类型",
  "msg": "详细错误信息",
  "timestamp": "2023-01-01T12:00:00.000Z"
}
```

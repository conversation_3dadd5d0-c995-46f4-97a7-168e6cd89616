<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="container mx-auto px-4">
      <h1 class="text-4xl font-bold text-center mb-8 text-gray-900">
        新闻详情 API 测试
      </h1>
      
      <div class="max-w-2xl mx-auto">
        <!-- API 测试表单 -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 class="text-xl font-semibold mb-4">测试新闻详情 API</h2>
          
          <div class="space-y-4">
            <div>
              <label for="newsId" class="block text-sm font-medium text-gray-700 mb-2">
                新闻 ID
              </label>
              <input
                id="newsId"
                v-model="newsId"
                type="text"
                placeholder="请输入新闻ID，例如：975911080450785280"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div class="flex gap-4">
              <button
                @click="testGetAPI"
                :disabled="!newsId || loading"
                class="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {{ loading ? '测试中...' : '测试 GET API' }}
              </button>
              
              <button
                @click="testPostAPI"
                :disabled="!newsId || loading"
                class="flex-1 bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {{ loading ? '测试中...' : '测试 POST API' }}
              </button>
            </div>
            
            <div>
              <NuxtLink
                :to="`/news/${newsId}`"
                v-if="newsId"
                class="block w-full text-center bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700"
              >
                查看新闻详情页面
              </NuxtLink>
            </div>
          </div>
        </div>

        <!-- 测试结果 -->
        <div v-if="result" class="bg-white rounded-lg shadow-lg p-6">
          <h3 class="text-lg font-semibold mb-4">测试结果</h3>
          
          <div class="mb-4">
            <span class="inline-block px-3 py-1 rounded-full text-sm font-medium"
                  :class="result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
              {{ result.success ? '成功' : '失败' }}
            </span>
            <span class="ml-2 text-sm text-gray-600">
              {{ result.timestamp }}
            </span>
          </div>

          <div v-if="result.success && result.data" class="space-y-4">
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <strong>新闻标题：</strong>
                <p class="text-gray-700">{{ result.data.title }}</p>
              </div>
              <div>
                <strong>发布时间：</strong>
                <p class="text-gray-700">{{ result.data.publishTime }}</p>
              </div>
              <div>
                <strong>浏览量：</strong>
                <p class="text-gray-700">{{ result.data.viewCount }}</p>
              </div>
              <div>
                <strong>来源：</strong>
                <p class="text-gray-700">{{ result.data.source }}</p>
              </div>
            </div>
            
            <div v-if="result.data.introduction" class="text-sm">
              <strong>简介：</strong>
              <p class="text-gray-700 mt-1">{{ result.data.introduction }}</p>
            </div>
            
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <strong>分类数量：</strong>
                <p class="text-gray-700">{{ result.data.classifyInfo?.length || 0 }}</p>
              </div>
              <div>
                <strong>标签数量：</strong>
                <p class="text-gray-700">{{ result.data.labelInfo?.length || 0 }}</p>
              </div>
            </div>
          </div>

          <div v-else-if="!result.success" class="text-red-600">
            <p><strong>错误：</strong>{{ result.error }}</p>
            <p><strong>消息：</strong>{{ result.message }}</p>
          </div>

          <!-- 原始响应数据 -->
          <details class="mt-4">
            <summary class="cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900">
              查看原始响应数据
            </summary>
            <pre class="mt-2 p-4 bg-gray-100 rounded text-xs overflow-auto">{{ JSON.stringify(result, null, 2) }}</pre>
          </details>
        </div>

        <!-- 预设测试用例 -->
        <div class="bg-white rounded-lg shadow-lg p-6 mt-8">
          <h3 class="text-lg font-semibold mb-4">预设测试用例</h3>
          
          <div class="space-y-2">
            <button
              @click="newsId = '975911080450785280'"
              class="block w-full text-left px-4 py-2 bg-gray-50 hover:bg-gray-100 rounded border"
            >
              <strong>有效新闻ID：</strong>975911080450785280
            </button>
            
            <button
              @click="newsId = '999999999999999999'"
              class="block w-full text-left px-4 py-2 bg-gray-50 hover:bg-gray-100 rounded border"
            >
              <strong>不存在的新闻ID：</strong>999999999999999999
            </button>
            
            <button
              @click="newsId = 'invalid-id'"
              class="block w-full text-left px-4 py-2 bg-gray-50 hover:bg-gray-100 rounded border"
            >
              <strong>无效格式ID：</strong>invalid-id
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { NewsDetailResponse } from '~/server/api/news/types'

// 页面标题
useHead({
  title: '新闻详情 API 测试'
})

// 响应式数据
const newsId = ref('975911080450785280')
const loading = ref(false)
const result = ref<NewsDetailResponse | null>(null)

// 测试 GET API
async function testGetAPI() {
  if (!newsId.value) return
  
  loading.value = true
  result.value = null
  
  try {
    const response = await $fetch<NewsDetailResponse>(`/api/news/${newsId.value}`)
    result.value = response
  } catch (error: any) {
    result.value = {
      success: false,
      error: error.data?.error || '请求失败',
      message: error.data?.message || error.message || '未知错误',
      timestamp: new Date().toISOString()
    }
  } finally {
    loading.value = false
  }
}

// 测试 POST API
async function testPostAPI() {
  if (!newsId.value) return
  
  loading.value = true
  result.value = null
  
  try {
    const response = await $fetch<NewsDetailResponse>(`/api/news/${newsId.value}`, {
      method: 'POST',
      body: {
        options: {
          includeContent: true,
          includeSeo: true,
          includeClassify: true,
          includeLabels: true
        }
      }
    })
    result.value = response
  } catch (error: any) {
    result.value = {
      success: false,
      error: error.data?.error || '请求失败',
      message: error.data?.message || error.message || '未知错误',
      timestamp: new Date().toISOString()
    }
  } finally {
    loading.value = false
  }
}
</script>

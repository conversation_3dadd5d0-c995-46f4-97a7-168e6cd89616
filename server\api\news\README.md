# 新闻详情 API 文档

## 概述

本 API 提供新闻详情查询功能，从原有的 `db/index.ts` 迁移而来，现在使用 Nuxt 3 的 API 路由和多数据库连接管理器。

## API 端点

### 1. 查询新闻列表 (POST)

**新增功能**

- **URL**: `/api/news`
- **方法**: `POST`
- **描述**: 查询资讯列表数据，支持分页和分类筛选

#### 请求参数

| 参数 | 类型 | 位置 | 必需 | 描述 |
|------|------|------|------|------|
| page | number | 请求体 | 否 | 页码，默认为1，最小为1 |
| pageSize | number | 请求体 | 否 | 每页数量，默认为10，范围1-100 |
| limit | number | 请求体 | 否 | 每页数量的别名，与pageSize功能相同 |
| categoryId | number | 请求体 | 否 | 分类ID，用于按分类筛选资讯 |

#### 请求体格式

```typescript
{
  page?: number;           // 页码，默认为1
  pageSize?: number;       // 每页数量，默认为10
  limit?: number;          // 每页数量的别名，与pageSize功能相同
  categoryId?: number;     // 分类ID，用于按分类筛选
}
```

#### 响应格式

```typescript
{
  success: boolean;
  data?: {
    data: NewsListItem[];     // 新闻列表数组
    total: number;            // 总记录数
    page: number;             // 当前页码
    pageSize: number;         // 每页数量
    totalPages: number;       // 总页数
  };
  msg?: string;
  error?: string;
  timestamp: string;
}
```

#### 示例请求

```bash
POST /api/news
Content-Type: application/json

{
  "page": 1,
  "pageSize": 10,
  "categoryId": 1
}
```

#### 示例响应

```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": "975911080450785280",
        "title": "新闻标题",
        "shortTitle": "简短标题",
        "introduction": "新闻简介",
        "picture": "图片URL",
        "source": "新闻来源",
        "publishTime": "2023-01-01 12:00:00",
        "viewCount": 1000,
        "isPublish": 1,
        "cateId": 1,
        "newsClassifys": "1,2,3",
        "newsLabels": "1,2,3",
        "createdAt": "2023-01-01 12:00:00",
        "updatedAt": "2023-01-01 12:00:00"
      }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 10,
    "totalPages": 10
  },
  "msg": "查询成功",
  "timestamp": "2023-01-01T12:00:00.000Z"
}
```

### 2. 获取新闻详情 (GET)

**推荐使用此方法**

- **URL**: `/api/news/{id}`
- **方法**: `GET`
- **描述**: 根据新闻ID获取完整的新闻详情信息

#### 请求参数

| 参数 | 类型 | 位置 | 必需 | 描述 |
|------|------|------|------|------|
| id | string | 路径参数 | 是 | 新闻ID |

#### 响应格式

```typescript
{
  success: boolean;
  data?: NewsWithDetails;
  message?: string;
  error?: string;
  timestamp: string;
}
```

#### 示例请求

```bash
GET /api/news/975911080450785280
```

#### 示例响应

```json
{
  "success": true,
  "data": {
    "id": "975911080450785280",
    "title": "新闻标题",
    "shortTitle": "简短标题",
    "introduction": "新闻简介",
    "picture": "图片URL",
    "source": "新闻来源",
    "publishTime": "2023-01-01 12:00:00",
    "viewCount": 1000,
    "isPublish": 1,
    "cateId": 1,
    "newsClassifys": "1,2,3",
    "newsLabels": "1,2,3",
    "createdAt": "2023-01-01 12:00:00",
    "updatedAt": "2023-01-01 12:00:00",
    "seoInfo": {
      "id": "seo_id",
      "seoTitle": "SEO标题",
      "seoKeyword": "关键词",
      "seoDescription": "SEO描述",
      "seoType": 3
    },
    "contentInfo": {
      "id": "content_id",
      "content": "新闻内容...",
      "createdAt": "2023-01-01 12:00:00",
      "updatedAt": "2023-01-01 12:00:00"
    },
    "classifyInfo": [
      {
        "id": "1",
        "classifyName": "分类名称",
        "uniqueIdentification": "unique_id",
        "status": 1
      }
    ],
    "labelInfo": [
      {
        "id": "1",
        "labelName": "标签名称",
        "uniqueIdentification": "unique_id",
        "sorting": 1,
        "status": 1,
        "newsCount": 10,
        "createdAt": "2023-01-01 12:00:00",
        "updatedAt": "2023-01-01 12:00:00"
      }
    ]
  },
  "message": "查询成功",
  "timestamp": "2023-01-01T12:00:00.000Z"
}
```

### 3. 获取新闻详情 (POST)

- **URL**: `/api/news/{id}`
- **方法**: `POST`
- **描述**: 根据新闻ID获取新闻详情信息，支持自定义查询选项

#### 请求参数

| 参数 | 类型 | 位置 | 必需 | 描述 |
|------|------|------|------|------|
| id | string | 路径参数 | 是 | 新闻ID |
| options | object | 请求体 | 否 | 查询选项 |

#### 请求体格式

```typescript
{
  options?: {
    includeContent?: boolean;    // 是否包含内容信息，默认true
    includeSeo?: boolean;        // 是否包含SEO信息，默认true
    includeClassify?: boolean;   // 是否包含分类信息，默认true
    includeLabels?: boolean;     // 是否包含标签信息，默认true
  }
}
```

#### 示例请求

```bash
POST /api/news/975911080450785280
Content-Type: application/json

{
  "options": {
    "includeContent": true,
    "includeSeo": true,
    "includeClassify": true,
    "includeLabels": false
  }
}
```

## 错误响应

### 400 Bad Request

```json
{
  "success": false,
  "error": "缺少新闻ID参数",
  "message": "请提供有效的新闻ID",
  "timestamp": "2023-01-01T12:00:00.000Z"
}
```

### 404 Not Found

```json
{
  "success": false,
  "error": "新闻不存在",
  "message": "未找到ID为 123456 的新闻记录",
  "timestamp": "2023-01-01T12:00:00.000Z"
}
```

### 500 Internal Server Error

```json
{
  "success": false,
  "error": "服务器内部错误",
  "message": "查询新闻详情时发生错误，请稍后重试",
  "timestamp": "2023-01-01T12:00:00.000Z"
}
```

## 数据类型定义

### NewsWithDetails

完整的新闻详情对象，包含新闻基本信息、SEO信息、内容信息、分类信息和标签信息。

### NewsClassifyInfo

新闻分类信息对象。

### NewsLabelInfo

新闻标签信息对象。

## 功能特性

1. **多数据库支持**: 使用新的多数据库连接管理器
2. **输入验证**: 验证新闻ID格式
3. **错误处理**: 完善的错误处理和HTTP状态码
4. **请求日志**: 记录请求和响应时间
5. **缓存支持**: GET请求支持缓存头设置
6. **灵活查询**: POST方法支持自定义查询选项

## 性能优化

1. **并行查询**: 分类和标签信息并行获取
2. **条件查询**: 只有当相关字段不为空时才执行查询
3. **连接池**: 使用数据库连接池管理连接
4. **缓存**: 支持HTTP缓存头设置

## 迁移说明

从 `db/index.ts` 迁移的功能：

- ✅ `getNewsWithDetails` 函数
- ✅ `getNewsClassifyDetails` 函数
- ✅ `getNewsLabelDetails` 函数
- ✅ 相关 TypeScript 接口定义
- ✅ 使用多数据库连接管理器
- ✅ 保持原有业务逻辑不变

新增功能：

- ✅ `getNewsList` 函数 - 新闻列表查询
- ✅ `validateNewsListParams` 函数 - 列表查询参数验证
- ✅ 新闻列表 POST API 接口 (`/api/news`)
- ✅ 支持分页查询（page, pageSize/limit）
- ✅ 支持分类筛选（categoryId）
- ✅ 完整的输入验证和错误处理
- ✅ 相关 TypeScript 类型定义

## 测试

运行新闻详情测试：

```bash
npx tsx server/api/news/test.ts
```

运行新闻列表测试：

```bash
npx tsx server/api/news/list-test.ts
```

## 注意事项

1. 新闻ID必须为纯数字格式
2. 推荐使用GET方法获取数据
3. POST方法主要用于需要自定义查询选项的场景
4. API响应包含时间戳用于调试和日志记录

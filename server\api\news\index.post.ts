/**
 * 查询新闻列表 API 路由 (POST 方法)
 * 路径: /api/news
 * 方法: POST
 * 
 * 功能: 查询资讯列表数据，支持分页和分类筛选
 */

import { getNewsList, validateNewsListParams } from './service';
import type { NewsListResponse, ErrorResponse, NewsListRequest } from './types';

export default defineEventHandler(async (event): Promise<NewsListResponse | ErrorResponse> => {
  const startTime = Date.now();
  
  try {
    // 解析请求体
    let requestBody: NewsListRequest = {};
    try {
      const body = await readBody(event);
      if (body && typeof body === 'object') {
        requestBody = body as NewsListRequest;
      }
    } catch (error) {
      // 如果解析请求体失败，返回错误
      console.warn('解析请求体失败:', error);
      setResponseStatus(event, 400);
      return {
        success: false,
        error: '请求体格式错误',
        msg: '请提供有效的JSON格式请求体',
        timestamp: new Date().toISOString()
      };
    }

    // 验证查询参数
    let validatedParams;
    try {
      validatedParams = validateNewsListParams(requestBody);
    } catch (error) {
      setResponseStatus(event, 400);
      return {
        success: false,
        error: '参数验证失败',
        msg: '请检查查询参数格式是否正确',
        timestamp: new Date().toISOString()
      };
    }

    // 记录请求日志
    console.log(`[${new Date().toISOString()}] POST /api/news - 开始查询新闻列表`, {
      params: validatedParams,
      originalParams: requestBody
    });

    // 查询新闻列表
    const newsListData = await getNewsList(requestBody);

    // 计算响应时间
    const responseTime = Date.now() - startTime;
    console.log(`[${new Date().toISOString()}] POST /api/news - 查询成功，耗时: ${responseTime}ms`, {
      total: newsListData.total,
      page: newsListData.page,
      pageSize: newsListData.pageSize,
      totalPages: newsListData.totalPages,
      dataCount: newsListData.data.length
    });

    // 返回成功响应
    setResponseStatus(event, 200);
    return {
      success: true,
      data: newsListData,
      msg: '查询成功',
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    // 计算响应时间
    const responseTime = Date.now() - startTime;
    
    // 记录错误日志
    console.error(`[${new Date().toISOString()}] POST /api/news - 查询失败，耗时: ${responseTime}ms`, error);

    // 返回服务器错误响应
    setResponseStatus(event, 500);
    return {
      success: false,
      error: '服务器内部错误',
      msg: '查询新闻列表时发生错误，请稍后重试',
      timestamp: new Date().toISOString()
    };
  }
});

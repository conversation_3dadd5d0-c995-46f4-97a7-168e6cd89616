/**
 * 新闻 API 相关的 TypeScript 接口定义
 * 从 db/index.ts 迁移而来
 */

// 定义分类信息类型
export interface NewsClassifyInfo {
  id: string;
  classifyName: string | null;
  uniqueIdentification: string | null;
  status: number | null;
}

// 定义标签信息类型
export interface NewsLabelInfo {
  id: string;
  labelName: string | null;
  uniqueIdentification: string | null;
  sorting: number | null;
  status: number | null;
  newsCount: number | null;
  createdAt: string | null;
  updatedAt: string | null;
}

// 定义返回类型
export interface NewsWithDetails {
  id: string;
  title: string | null;
  shortTitle: string | null;
  introduction: string | null;
  picture: string | null;
  source: string | null;
  publishTime: string | null;
  viewCount: number | null;
  isPublish: number | null;
  cateId: number | null;
  newsClassifys: string | null;
  newsLabels: string | null;
  createdAt: string | null;
  updatedAt: string | null;
  seoInfo: {
    id: string | null;
    seoTitle: string | null;
    seoKeyword: string | null;
    seoDescription: string | null;
    seoType: number | null;
  } | null;
  contentInfo: {
    id: string | null;
    content: string | null;
    createdAt: string | null;
    updatedAt: string | null;
  } | null;
  classifyInfo: NewsClassifyInfo[];
  labelInfo: NewsLabelInfo[];
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  msg?: string;
  error?: string;
  timestamp: string;
}

// 新闻详情 API 响应类型
export interface NewsDetailResponse extends ApiResponse<NewsWithDetails> {}

// 错误响应类型
export interface ErrorResponse extends ApiResponse<null> {
  success: false;
  error: string;
}

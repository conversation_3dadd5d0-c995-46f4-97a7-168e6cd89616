/**
 * 新闻 API 相关的 TypeScript 接口定义
 * 从 db/index.ts 迁移而来
 */

// 定义分类信息类型
export interface NewsClassifyInfo {
  id: string;
  classifyName: string | null;
  uniqueIdentification: string | null;
  status: number | null;
}

// 定义标签信息类型
export interface NewsLabelInfo {
  id: string;
  labelName: string | null;
  uniqueIdentification: string | null;
  sorting: number | null;
  status: number | null;
  newsCount: number | null;
  createdAt: string | null;
  updatedAt: string | null;
}

// 定义返回类型
export interface NewsWithDetails {
  id: string;
  title: string | null;
  shortTitle: string | null;
  introduction: string | null;
  picture: string | null;
  source: string | null;
  publishTime: string | null;
  viewCount: number | null;
  isPublish: number | null;
  cateId: number | null;
  newsClassifys: string | null;
  newsLabels: string | null;
  createdAt: string | null;
  updatedAt: string | null;
  seoInfo: {
    id: string | null;
    seoTitle: string | null;
    seoKeyword: string | null;
    seoDescription: string | null;
    seoType: number | null;
  } | null;
  contentInfo: {
    id: string | null;
    content: string | null;
    createdAt: string | null;
    updatedAt: string | null;
  } | null;
  classifyInfo: NewsClassifyInfo[];
  labelInfo: NewsLabelInfo[];
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  msg?: string;
  error?: string;
  timestamp: string;
}

// 新闻详情 API 响应类型
export interface NewsDetailResponse extends ApiResponse<NewsWithDetails> {}

// 错误响应类型
export interface ErrorResponse extends ApiResponse<null> {
  success: false;
  error: string;
}

// 新闻列表查询请求参数类型
export interface NewsListRequest {
  page?: number;           // 页码，默认为1
  pageSize?: number;       // 每页数量，默认为10
  limit?: number;          // 每页数量的别名，与pageSize功能相同
  categoryId?: number;     // 分类ID，用于按分类筛选
}

// 新闻列表项类型（简化版的新闻信息）
export interface NewsListItem {
  id: string;
  title: string | null;
  shortTitle: string | null;
  introduction: string | null;
  picture: string | null;
  source: string | null;
  publishTime: string | null;
  viewCount: number | null;
  isPublish: number | null;
  cateId: number | null;
  newsClassifys: string | null;
  newsLabels: string | null;
  createdAt: string | null;
  updatedAt: string | null;
  // 新增：分类和标签的详细信息
  classifyInfo: NewsClassifyInfo[];
  labelInfo: NewsLabelInfo[];
}

// 新闻列表响应数据类型
export interface NewsListData {
  data: NewsListItem[];     // 新闻列表数组
  total: number;            // 总记录数
  page: number;             // 当前页码
  pageSize: number;         // 每页数量
  totalPages: number;       // 总页数
}

// 新闻列表 API 响应类型
export interface NewsListResponse extends ApiResponse<NewsListData> {}

<template>
  <div class="news-detail-page">
    <div class="container mx-auto px-4 py-8">
      <!-- <h1 class="text-3xl font-bold mb-6">新闻详情</h1> -->
      
      <!-- 加载状态 -->
      <div v-if="pending" class="text-center py-8">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p class="mt-2 text-gray-600">正在加载...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-6">
        <h2 class="text-red-800 font-semibold mb-2">加载失败</h2>
        <!-- {{ error.data }} -->
        <p class="text-red-600">{{ error.data.msg || error.message || '获取详情时发生错误' }}</p>
        <button 
          @click="refresh()" 
          class="mt-4 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
        >
          重试
        </button>
      </div>

      <!-- 新闻内容 -->
      <div v-else-if="data?.success && data.data" class="news-content">
        <article class="bg-white rounded-lg shadow-lg p-6">
          <!-- 新闻头部信息 -->
          <header class="mb-6">
            <h1 class="text-2xl font-bold text-gray-900 mb-2">{{ data.data.title }}</h1>
            <div v-if="data.data.shortTitle" class="text-lg text-gray-600 mb-4">
              {{ data.data.shortTitle }}
            </div>
            
            <!-- 新闻元信息 -->
            <div class="flex flex-wrap gap-4 text-sm text-gray-500 mb-4">
              <span v-if="data.data.source">来源：{{ data.data.source }}</span>
              <span v-if="data.data.publishTime">发布时间：{{ formatDate(data.data.publishTime) }}</span>
              <span v-if="data.data.viewCount">浏览量：{{ data.data.viewCount }}</span>
            </div>

            <!-- 新闻图片 -->
            <img 
              v-if="data.data.picture" 
              :src="data.data.picture" 
              :alt="data.data.title"
              class="w-full max-w-2xl h-auto rounded-lg mb-4"
            />

            <!-- 新闻简介 -->
            <div v-if="data.data.introduction" class="text-gray-700 bg-gray-50 p-4 rounded-lg mb-6">
              {{ data.data.introduction }}
            </div>
          </header>

          <!-- 新闻内容 -->
          <div v-if="data.data.contentInfo?.content" class="prose max-w-none mb-8">
            <div v-html="data.data.contentInfo.content" class="text-gray-800 leading-relaxed"></div>
          </div>

          <!-- 分类和标签 -->
          <footer class="border-t pt-6">
            <!-- 分类信息 -->
            <div v-if="data.data.classifyInfo?.length" class="mb-4">
              <h3 class="text-sm font-semibold text-gray-700 mb-2">分类：</h3>
              <div class="flex flex-wrap gap-2">
                <span 
                  v-for="classify in data.data.classifyInfo" 
                  :key="classify.id"
                  class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm"
                >
                  {{ classify.classifyName }}
                </span>
              </div>
            </div>

            <!-- 标签信息 -->
            <div v-if="data.data.labelInfo?.length" class="mb-4">
              <h3 class="text-sm font-semibold text-gray-700 mb-2">标签：</h3>
              <div class="flex flex-wrap gap-2">
                <span 
                  v-for="label in data.data.labelInfo" 
                  :key="label.id"
                  class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm"
                >
                  {{ label.labelName }}
                </span>
              </div>
            </div>
          </footer>
        </article>

        <!-- SEO 信息（开发模式下显示） -->
        <div v-if="data.data.seoInfo && isDev" class="mt-6 bg-gray-50 rounded-lg p-4">
          <h3 class="font-semibold text-gray-700 mb-2">SEO 信息（开发模式）</h3>
          <div class="text-sm text-gray-600 space-y-1">
            <p><strong>SEO 标题：</strong>{{ data.data.seoInfo.seoTitle }}</p>
            <p><strong>SEO 关键词：</strong>{{ data.data.seoInfo.seoKeyword }}</p>
            <p><strong>SEO 描述：</strong>{{ data.data.seoInfo.seoDescription }}</p>
          </div>
        </div>

        <!-- API 响应信息（开发模式下显示） -->
        <div v-if="isDev" class="mt-6 bg-yellow-50 rounded-lg p-4">
          <h3 class="font-semibold text-gray-700 mb-2">API 响应信息（开发模式）</h3>
          <div class="text-sm text-gray-600 space-y-1">
            <p><strong>响应时间：</strong>{{ data.timestamp }}</p>
            <p><strong>新闻ID：</strong>{{ data.data.id }}</p>
            <p><strong>分类数量：</strong>{{ data.data.classifyInfo?.length || 0 }}</p>
            <p><strong>标签数量：</strong>{{ data.data.labelInfo?.length || 0 }}</p>
          </div>
        </div>
      </div>

      <!-- 未找到新闻 -->
      <div v-else class="text-center py-8">
        <h2 class="text-xl font-semibold text-gray-700 mb-2">未找到新闻</h2>
        <p class="text-gray-600">请检查新闻ID是否正确</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { NewsDetailResponse } from '~/server/api/news/types'

// 获取路由参数
const route = useRoute()
const newsId = route.params.id as string

// 设置页面标题
// useHead({
//   title: `新闻详情 - ${newsId}`
// })

// 检查是否为开发模式
const isDev = process.dev

// 获取新闻详情数据
const { data, pending, error, refresh } = await useFetch<NewsDetailResponse>(`/api/news/${newsId}`,{
  method:'POST'
})

// 设置动态页面标题
watchEffect(() => {
  if (data.value?.success && data.value.data?.title) {
    useHead({
      title: data.value.data.title,
      meta: [
        { name: 'description', content: data.value.data.introduction || data.value.data.seoInfo?.seoDescription || '' },
        { name: 'keywords', content: data.value.data.seoInfo?.seoKeyword || '' }
      ]
    })
  }
})

// 格式化日期
function formatDate(dateString: string | null): string {
  if (!dateString) return ''
  try {
    return new Date(dateString).toLocaleString('zh-CN')
  } catch {
    return dateString
  }
}
</script>

<style scoped>
.news-detail-page {
  min-height: 100vh;
  background-color: #f9fafb;
}

.prose {
  line-height: 1.8;
}

.prose img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 1rem 0;
}

.prose p {
  margin-bottom: 1rem;
}

.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
  margin-top: 1.5rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}
</style>

/**
 * 获取新闻详情 API 路由 (POST 方法)
 * 路径: /api/news/{id}
 * 方法: POST
 * 
 */

import { getNewsWithDetails, validateNewsId } from './service';
import type { NewsDetailResponse, ErrorResponse } from './types';

// 请求体类型定义
interface NewsDetailRequest {
  options?: {
    includeContent?: boolean;
    includeSeo?: boolean;
    includeClassify?: boolean;
    includeLabels?: boolean;
  };
}

export default defineEventHandler(async (event): Promise<NewsDetailResponse | ErrorResponse> => {
  const startTime = Date.now();
  
  try {
    // 获取路径参数中的新闻ID
    const newsId = getRouterParam(event, 'id');
    
    // 验证新闻ID
    if (!newsId) {
      setResponseStatus(event, 400);
      return {
        success: false,
        error: '缺少新闻ID参数',
        msg: '请提供有效的新闻ID',
        timestamp: new Date().toISOString()
      };
    }

    // 验证新闻ID格式
    if (!validateNewsId(newsId)) {
      setResponseStatus(event, 400);
      return {
        success: false,
        error: '无效的新闻ID格式',
        msg: '新闻ID格式不正确，请检查后重试',
        timestamp: new Date().toISOString()
      };
    }

    // 解析请求体（可选的查询选项）
    let requestBody: NewsDetailRequest = {};
    try {
      const body = await readBody(event);
      if (body && typeof body === 'object') {
        requestBody = body as NewsDetailRequest;
      }
    } catch (error) {
      // 如果解析请求体失败，使用默认选项
      console.warn('解析请求体失败，使用默认选项:', error);
    }

    // 记录请求日志
    console.log(`[${new Date().toISOString()}] POST /api/news/${newsId} - 开始查询新闻详情`, {
      options: requestBody.options
    });

    // 查询新闻详情
    const newsDetail = await getNewsWithDetails(newsId);

    if (!newsDetail) {
      setResponseStatus(event, 404);
      return {
        success: false,
        error: '新闻不存在',
        msg: `未找到ID为 ${newsId} 的新闻记录`,
        timestamp: new Date().toISOString()
      };
    }

    // 根据请求选项过滤返回数据（可选功能）
    const options = requestBody.options || {};
    const filteredNewsDetail = {
      ...newsDetail,
      // 根据选项决定是否包含内容
      contentInfo: options.includeContent !== false ? newsDetail.contentInfo : null,
      // 根据选项决定是否包含SEO信息
      seoInfo: options.includeSeo !== false ? newsDetail.seoInfo : null,
      // 根据选项决定是否包含分类信息
      classifyInfo: options.includeClassify !== false ? newsDetail.classifyInfo : [],
      // 根据选项决定是否包含标签信息
      labelInfo: options.includeLabels !== false ? newsDetail.labelInfo : [],
    };

    // 计算响应时间
    const responseTime = Date.now() - startTime;
    console.log(`[${new Date().toISOString()}] POST /api/news/${newsId} - 查询成功，耗时: ${responseTime}ms`);

    // 返回成功响应
    setResponseStatus(event, 200);
    return {
      success: true,
      data: filteredNewsDetail,
      msg: '成功',
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    // 计算响应时间
    const responseTime = Date.now() - startTime;
    
    // 记录错误日志
    console.error(`[${new Date().toISOString()}] POST /api/news/${getRouterParam(event, 'id')} - 查询失败，耗时: ${responseTime}ms`, error);

    // 返回服务器错误响应
    setResponseStatus(event, 500);
    return {
      success: false,
      error: '服务器内部错误',
      msg: '查询新闻详情时发生错误，请稍后重试',
      timestamp: new Date().toISOString()
    };
  }
});

/**
 * 新闻列表 API 测试文件
 * 用于测试新闻列表查询功能
 */

import { getNewsList, validateNewsListParams } from './service';
import type { NewsListRequest } from './types';

/**
 * 测试新闻列表查询功能
 */
export async function testNewsListAPI() {
  console.log('=== 开始测试新闻列表 API ===');

  // 测试用例1：基本查询（默认参数）
  console.log('\n测试用例1: 基本查询（默认参数）');
  try {
    const result1 = await getNewsList({});
    console.log('✅ 基本查询成功');
    console.log(`总记录数: ${result1.total}`);
    console.log(`当前页: ${result1.page}`);
    console.log(`每页数量: ${result1.pageSize}`);
    console.log(`总页数: ${result1.totalPages}`);
    console.log(`返回数据条数: ${result1.data.length}`);
    if (result1.data.length > 0) {
      console.log(`第一条新闻标题: ${result1.data[0].title}`);
      console.log(`分类信息: ${result1.data[0].classifyInfo.map(c => c.classifyName).join(', ')}`);
      console.log(`标签信息: ${result1.data[0].labelInfo.map(l => l.labelName).join(', ')}`);
    }
  } catch (error) {
    console.error('❌ 基本查询失败:', error);
  }

  // 测试用例2：分页查询
  console.log('\n测试用例2: 分页查询（第2页，每页5条）');
  try {
    const result2 = await getNewsList({
      page: 2,
      pageSize: 5
    });
    console.log('✅ 分页查询成功');
    console.log(`总记录数: ${result2.total}`);
    console.log(`当前页: ${result2.page}`);
    console.log(`每页数量: ${result2.pageSize}`);
    console.log(`总页数: ${result2.totalPages}`);
    console.log(`返回数据条数: ${result2.data.length}`);
  } catch (error) {
    console.error('❌ 分页查询失败:', error);
  }

  // 测试用例3：使用limit参数
  console.log('\n测试用例3: 使用limit参数（limit=3）');
  try {
    const result3 = await getNewsList({
      limit: 3
    });
    console.log('✅ limit参数查询成功');
    console.log(`总记录数: ${result3.total}`);
    console.log(`当前页: ${result3.page}`);
    console.log(`每页数量: ${result3.pageSize}`);
    console.log(`返回数据条数: ${result3.data.length}`);
  } catch (error) {
    console.error('❌ limit参数查询失败:', error);
  }

  // 测试用例4：按分类筛选
  console.log('\n测试用例4: 按分类筛选（categoryId=1）');
  try {
    const result4 = await getNewsList({
      categoryId: 1,
      pageSize: 5
    });
    console.log('✅ 分类筛选查询成功');
    console.log(`总记录数: ${result4.total}`);
    console.log(`当前页: ${result4.page}`);
    console.log(`每页数量: ${result4.pageSize}`);
    console.log(`返回数据条数: ${result4.data.length}`);
    if (result4.data.length > 0) {
      console.log(`第一条新闻分类ID: ${result4.data[0].cateId}`);
      console.log(`分类信息: ${result4.data[0].classifyInfo.map(c => c.classifyName).join(', ')}`);
      console.log(`标签信息: ${result4.data[0].labelInfo.map(l => l.labelName).join(', ')}`);
    }
  } catch (error) {
    console.error('❌ 分类筛选查询失败:', error);
  }

  // 测试用例5：参数验证测试
  console.log('\n测试用例5: 参数验证测试');
  try {
    // 测试负数页码
    const params1: NewsListRequest = { page: -1, pageSize: 10 };
    const validated1 = validateNewsListParams(params1);
    console.log(`负数页码验证: page=${params1.page} -> ${validated1.page} ✅`);

    // 测试超大每页数量
    const params2: NewsListRequest = { page: 1, pageSize: 200 };
    const validated2 = validateNewsListParams(params2);
    console.log(`超大每页数量验证: pageSize=${params2.pageSize} -> ${validated2.pageSize} ✅`);

    // 测试无效分类ID
    const params3: NewsListRequest = { categoryId: -1 };
    const validated3 = validateNewsListParams(params3);
    console.log(`无效分类ID验证: categoryId=${params3.categoryId} -> ${validated3.categoryId} ✅`);

  } catch (error) {
    console.error('❌ 参数验证测试失败:', error);
  }

  console.log('\n=== 新闻列表 API 测试完成 ===');
}

// 直接执行测试
testNewsListAPI().catch(console.error);

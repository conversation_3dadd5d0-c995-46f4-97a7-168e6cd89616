/**
 * 新闻列表 API 性能测试文件
 * 测试关联查询的性能表现
 */

import { getNewsList } from './service';

/**
 * 性能测试函数
 */
export async function performanceTest() {
  console.log('=== 开始新闻列表 API 性能测试 ===');

  // 测试1: 小数据量查询性能
  console.log('\n测试1: 小数据量查询性能（5条记录）');
  const start1 = Date.now();
  try {
    const result1 = await getNewsList({ pageSize: 5 });
    const end1 = Date.now();
    console.log(`✅ 查询成功，耗时: ${end1 - start1}ms`);
    console.log(`返回记录数: ${result1.data.length}`);
    console.log(`平均每条记录耗时: ${((end1 - start1) / result1.data.length).toFixed(2)}ms`);
    
    // 检查分类和标签信息
    let totalClassifies = 0;
    let totalLabels = 0;
    result1.data.forEach(news => {
      totalClassifies += news.classifyInfo.length;
      totalLabels += news.labelInfo.length;
    });
    console.log(`总分类信息数: ${totalClassifies}`);
    console.log(`总标签信息数: ${totalLabels}`);
  } catch (error) {
    console.error('❌ 测试1失败:', error);
  }

  // 测试2: 中等数据量查询性能
  console.log('\n测试2: 中等数据量查询性能（20条记录）');
  const start2 = Date.now();
  try {
    const result2 = await getNewsList({ pageSize: 20 });
    const end2 = Date.now();
    console.log(`✅ 查询成功，耗时: ${end2 - start2}ms`);
    console.log(`返回记录数: ${result2.data.length}`);
    console.log(`平均每条记录耗时: ${((end2 - start2) / result2.data.length).toFixed(2)}ms`);
    
    // 检查分类和标签信息
    let totalClassifies = 0;
    let totalLabels = 0;
    result2.data.forEach(news => {
      totalClassifies += news.classifyInfo.length;
      totalLabels += news.labelInfo.length;
    });
    console.log(`总分类信息数: ${totalClassifies}`);
    console.log(`总标签信息数: ${totalLabels}`);
  } catch (error) {
    console.error('❌ 测试2失败:', error);
  }

  // 测试3: 大数据量查询性能
  console.log('\n测试3: 大数据量查询性能（50条记录）');
  const start3 = Date.now();
  try {
    const result3 = await getNewsList({ pageSize: 50 });
    const end3 = Date.now();
    console.log(`✅ 查询成功，耗时: ${end3 - start3}ms`);
    console.log(`返回记录数: ${result3.data.length}`);
    console.log(`平均每条记录耗时: ${((end3 - start3) / result3.data.length).toFixed(2)}ms`);
    
    // 检查分类和标签信息
    let totalClassifies = 0;
    let totalLabels = 0;
    result3.data.forEach(news => {
      totalClassifies += news.classifyInfo.length;
      totalLabels += news.labelInfo.length;
    });
    console.log(`总分类信息数: ${totalClassifies}`);
    console.log(`总标签信息数: ${totalLabels}`);
  } catch (error) {
    console.error('❌ 测试3失败:', error);
  }

  // 测试4: 分类筛选查询性能
  console.log('\n测试4: 分类筛选查询性能（categoryId=1，20条记录）');
  const start4 = Date.now();
  try {
    const result4 = await getNewsList({ categoryId: 1, pageSize: 20 });
    const end4 = Date.now();
    console.log(`✅ 查询成功，耗时: ${end4 - start4}ms`);
    console.log(`返回记录数: ${result4.data.length}`);
    if (result4.data.length > 0) {
      console.log(`平均每条记录耗时: ${((end4 - start4) / result4.data.length).toFixed(2)}ms`);
    }
    
    // 验证分类筛选是否正确
    const allCorrectCategory = result4.data.every(news => news.cateId === 1);
    console.log(`分类筛选正确性: ${allCorrectCategory ? '✅ 正确' : '❌ 错误'}`);
  } catch (error) {
    console.error('❌ 测试4失败:', error);
  }

  // 测试5: 连续查询性能（模拟并发）
  console.log('\n测试5: 连续查询性能测试（5次并发查询）');
  const start5 = Date.now();
  try {
    const promises = Array.from({ length: 5 }, (_, i) => 
      getNewsList({ page: i + 1, pageSize: 10 })
    );
    
    const results = await Promise.all(promises);
    const end5 = Date.now();
    
    console.log(`✅ 并发查询成功，总耗时: ${end5 - start5}ms`);
    console.log(`平均每次查询耗时: ${((end5 - start5) / 5).toFixed(2)}ms`);
    console.log(`总返回记录数: ${results.reduce((sum, result) => sum + result.data.length, 0)}`);
  } catch (error) {
    console.error('❌ 测试5失败:', error);
  }

  console.log('\n=== 新闻列表 API 性能测试完成 ===');
}

// 直接执行性能测试
performanceTest().catch(console.error);

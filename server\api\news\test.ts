/**
 * 新闻 API 测试文件
 * 用于测试迁移后的新闻详情查询功能
 */

import { getNewsWithDetails, validateNewsId } from './service';

/**
 * 测试新闻详情查询功能
 */
export async function testNewsDetailAPI() {
  console.log('=== 开始测试新闻详情 API ===');

  // 测试用例1：有效的新闻ID
  const testNewsId = '975911080450785280';
  console.log(`\n测试用例1: 查询新闻ID ${testNewsId}`);
  
  try {
    const result = await getNewsWithDetails(testNewsId);
    if (result) {
      console.log('✅ 查询成功');
      console.log(`新闻标题: ${result.title}`);
      console.log(`分类数量: ${result.classifyInfo.length}`);
      console.log(`标签数量: ${result.labelInfo.length}`);
      console.log(`是否有SEO信息: ${result.seoInfo?.id ? '是' : '否'}`);
      console.log(`是否有内容信息: ${result.contentInfo?.id ? '是' : '否'}`);
    } else {
      console.log('❌ 未找到新闻记录');
    }
  } catch (error) {
    console.error('❌ 查询失败:', error);
  }

  // 测试用例2：无效的新闻ID
  console.log('\n测试用例2: 查询不存在的新闻ID');
  try {
    const result = await getNewsWithDetails('999999999999999999');
    if (result) {
      console.log('❌ 意外找到了记录');
    } else {
      console.log('✅ 正确返回null（未找到记录）');
    }
  } catch (error) {
    console.error('❌ 查询失败:', error);
  }

  // 测试用例3：ID格式验证
  console.log('\n测试用例3: ID格式验证');
  const testIds = [
    { id: '123456789', expected: true, desc: '有效数字ID' },
    { id: '', expected: false, desc: '空字符串' },
    { id: 'abc123', expected: false, desc: '包含字母' },
    { id: '123-456', expected: false, desc: '包含特殊字符' },
    { id: '12345678901234567890123456789012345678901234567890123', expected: false, desc: '超长ID' }
  ];

  testIds.forEach(({ id, expected, desc }) => {
    const result = validateNewsId(id);
    const status = result === expected ? '✅' : '❌';
    console.log(`${status} ${desc}: "${id}" -> ${result} (期望: ${expected})`);
  });

  console.log('\n=== 新闻详情 API 测试完成 ===');
}

// 如果直接运行此文件，则执行测试
if (process.argv[1] && process.argv[1].includes('test')) {
  testNewsDetailAPI().catch(console.error);
}

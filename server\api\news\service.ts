/**
 * 新闻服务层
 * 从 db/index.ts 迁移的业务逻辑
 */

import { sql, eq, and, desc, count } from 'drizzle-orm';
import { getCoreNewsDB } from '../../../db/multi-db';
import { knNews, knNewsClassify, knNewsContent, knNewsSeo, knNewsLabel } from '../../../db/schema';
import type { NewsClassifyInfo, NewsLabelInfo, NewsWithDetails, NewsListItem, NewsListData, NewsListRequest } from './types';

/**
 * 根据分类ID字符串获取分类详情信息
 * @param classifyIds 分类ID字符串，格式如 "1,2,3"
 * @returns 分类信息数组
 */
export async function getNewsClassifyDetails(classifyIds: string | null): Promise<NewsClassifyInfo[]> {
  if (!classifyIds || classifyIds.trim() === '') {
    return [];
  }

  try {
    const db = getCoreNewsDB();
    
    // 将分类ID字符串分割成数组
    const idArray = classifyIds.split(',').map(id => id.trim()).filter(id => id !== '');

    if (idArray.length === 0) {
      return [];
    }

    // 查询分类信息
    const classifyResult = await db.select({
      id: knNewsClassify.id,
      classifyName: knNewsClassify.classifyName,
      uniqueIdentification: knNewsClassify.uniqueIdentification,
      status: knNewsClassify.status,
    })
    .from(knNewsClassify)
    .where(
      and(
        sql`${knNewsClassify.id} IN (${sql.join(idArray.map(id => sql`${id}`), sql`, `)})`,
        eq(knNewsClassify.deletedAt, 1) // 确保分类记录未被删除
      )
    );

    return classifyResult;
  } catch (error) {
    console.error('查询分类信息出错:', error);
    return [];
  }
}

/**
 * 根据标签ID字符串获取标签详情信息
 * @param labelIds 标签ID字符串，格式如 "1,2,3"
 * @returns 标签信息数组
 */
export async function getNewsLabelDetails(labelIds: string | null): Promise<NewsLabelInfo[]> {
  if (!labelIds || labelIds.trim() === '') {
    return [];
  }

  try {
    const db = getCoreNewsDB();
    
    // 将标签ID字符串分割成数组
    const idArray = labelIds.split(',').map(id => id.trim()).filter(id => id !== '');

    if (idArray.length === 0) {
      return [];
    }

    // 查询标签信息
    const labelResult = await db.select({
      id: knNewsLabel.id,
      labelName: knNewsLabel.labelName,
      uniqueIdentification: knNewsLabel.uniqueIdentification,
      sorting: knNewsLabel.sorting,
      status: knNewsLabel.status,
      newsCount: knNewsLabel.newsCount,
      createdAt: knNewsLabel.createdAt,
      updatedAt: knNewsLabel.updatedAt,
    })
    .from(knNewsLabel)
    .where(
      and(
        sql`${knNewsLabel.id} IN (${sql.join(idArray.map(id => sql`${id}`), sql`, `)})`,
        eq(knNewsLabel.deletedAt, 1) // 确保标签记录未被删除
      )
    );

    return labelResult;
  } catch (error) {
    console.error('查询标签信息出错:', error);
    return [];
  }
}

/**
 * 根据新闻ID查询新闻详情，包括SEO信息、内容信息、分类信息和标签信息
 * @param newsId 新闻ID
 * @returns 新闻详情信息，如果未找到则返回null
 */
export async function getNewsWithDetails(newsId: string): Promise<NewsWithDetails | null> {
  try {
    const db = getCoreNewsDB();
    
    const result = await db.select({
      // 新闻主表字段
      id: knNews.id,
      title: knNews.title,
      shortTitle: knNews.shortTitle,
      introduction: knNews.introduction,
      picture: knNews.picture,
      source: knNews.source,
      publishTime: knNews.publishTime,
      viewCount: knNews.viewCount,
      isPublish: knNews.isPublish,
      cateId: knNews.cateId,
      newsClassifys: knNews.newsClassifys,
      newsLabels: knNews.newsLabels,
      createdAt: knNews.createdAt,
      updatedAt: knNews.updatedAt,

      // SEO 信息
      seoInfo: {
        id: knNewsSeo.id,
        seoTitle: knNewsSeo.seoTitle,
        seoKeyword: knNewsSeo.seoKeyword,
        seoDescription: knNewsSeo.seoDescription,
        seoType: knNewsSeo.seoType,
      },

      // 内容信息
      contentInfo: {
        id: knNewsContent.id,
        content: knNewsContent.content,
        createdAt: knNewsContent.createdAt,
        updatedAt: knNewsContent.updatedAt,
      }
    })
    .from(knNews)
    // 左联接 SEO 表
    .leftJoin(
      knNewsSeo,
      and(
        eq(knNewsSeo.relationId, knNews.id),
        eq(knNewsSeo.deletedAt, 1) // 确保 SEO 记录未被删除
      )
    )
    // 左联接内容表
    .leftJoin(
      knNewsContent,
      and(
        eq(knNewsContent.newsId, knNews.id),
        eq(knNewsContent.deletedAt, 1) // 确保内容记录未被删除
      )
    )
    .where(
      and(
        eq(knNews.id, newsId), // 查询指定 ID 的新闻
        eq(knNews.deletedAt, 1) // 确保新闻记录未被删除
      )
    )
    .limit(1); // 限制返回一条记录

    if (result.length === 0) {
      return null;
    }

    const newsData = result[0];

    // 并行获取分类信息和标签信息以优化性能
    // 只有当相应字段不为空时才执行查询，避免不必要的数据库访问
    const classifyPromise = (newsData.newsClassifys && newsData.newsClassifys.trim() !== '')
      ? getNewsClassifyDetails(newsData.newsClassifys)
      : Promise.resolve<NewsClassifyInfo[]>([]);

    const labelPromise = (newsData.newsLabels && newsData.newsLabels.trim() !== '')
      ? getNewsLabelDetails(newsData.newsLabels)
      : Promise.resolve<NewsLabelInfo[]>([]);

    const [classifyInfo, labelInfo] = await Promise.all([classifyPromise, labelPromise]);

    // 组合完整的新闻详情
    const newsWithDetails: NewsWithDetails = {
      ...newsData,
      classifyInfo,
      labelInfo
    };

    return newsWithDetails;
  } catch (error) {
    console.error('查询新闻详情出错:', error);
    throw error;
  }
}

/**
 * 验证新闻ID格式
 * @param newsId 新闻ID
 * @returns 是否为有效格式
 */
export function validateNewsId(newsId: string): boolean {
  // 检查是否为空或只包含空白字符
  if (!newsId || newsId.trim() === '') {
    return false;
  }

  // 检查长度（假设新闻ID长度在1-50之间）
  if (newsId.length < 1 || newsId.length > 50) {
    return false;
  }

  // 检查是否只包含数字（根据您的数据库ID格式调整）
  // 如果您的ID包含其他字符，请修改这个正则表达式
  const idPattern = /^[0-9]+$/;
  return idPattern.test(newsId);
}

/**
 * 验证新闻列表查询参数
 * @param params 查询参数
 * @returns 验证后的参数
 */
export function validateNewsListParams(params: NewsListRequest): {
  page: number;
  pageSize: number;
  categoryId?: number;
} {
  // 处理页码，默认为1，最小为1
  let page = params.page || 1;
  if (page < 1) page = 1;

  // 处理每页数量，优先使用pageSize，其次使用limit，默认为10，范围1-100
  let pageSize = params.pageSize || params.limit || 10;
  if (pageSize < 1) pageSize = 1;
  if (pageSize > 100) pageSize = 100;

  // 处理分类ID
  let categoryId: number | undefined;
  if (params.categoryId !== undefined && params.categoryId !== null) {
    categoryId = Number(params.categoryId);
    // 如果转换失败或为负数，则忽略此参数
    if (isNaN(categoryId) || categoryId < 0) {
      categoryId = undefined;
    }
  }

  return { page, pageSize, categoryId };
}

/**
 * 查询新闻列表
 * @param params 查询参数
 * @returns 新闻列表数据
 */
export async function getNewsList(params: NewsListRequest): Promise<NewsListData> {
  try {
    const db = getCoreNewsDB();

    // 验证和处理查询参数
    const { page, pageSize, categoryId } = validateNewsListParams(params);

    // 构建查询条件
    const whereConditions = [
      eq(knNews.deletedAt, 1), // 确保新闻记录未被删除
      eq(knNews.isPublish, 1)  // 只查询已发布的新闻
    ];

    // 如果指定了分类ID，添加分类筛选条件
    if (categoryId !== undefined) {
      whereConditions.push(eq(knNews.cateId, categoryId));
    }

    // 查询总记录数
    const totalResult = await db.select({
      count: count()
    })
    .from(knNews)
    .where(and(...whereConditions));

    const total = totalResult[0]?.count || 0;

    // 计算总页数
    const totalPages = Math.ceil(total / pageSize);

    // 计算偏移量
    const offset = (page - 1) * pageSize;

    // 查询新闻列表数据
    const newsListResult = await db.select({
      id: knNews.id,
      title: knNews.title,
      shortTitle: knNews.shortTitle,
      introduction: knNews.introduction,
      picture: knNews.picture,
      source: knNews.source,
      publishTime: knNews.publishTime,
      viewCount: knNews.viewCount,
      isPublish: knNews.isPublish,
      cateId: knNews.cateId,
      newsClassifys: knNews.newsClassifys,
      newsLabels: knNews.newsLabels,
      createdAt: knNews.createdAt,
      updatedAt: knNews.updatedAt,
    })
    .from(knNews)
    .where(and(...whereConditions))
    .orderBy(desc(knNews.publishTime), desc(knNews.createdAt)) // 按发布时间和创建时间倒序排列
    .limit(pageSize)
    .offset(offset);

    return {
      data: newsListResult,
      total,
      page,
      pageSize,
      totalPages
    };

  } catch (error) {
    console.error('查询新闻列表出错:', error);
    throw error;
  }
}
